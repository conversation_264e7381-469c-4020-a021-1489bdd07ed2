{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "twostep", "title": "twostep", "description": "A minimalistic twostep component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "twostep.tsx", "content": "'use client';\nimport React, { useState, useRef, useEffect } from 'react';\n\nexport default function TwoStep() {\n  const [code, setCode] = useState<string[]>(new Array(5).fill(''));\n  const [focusedIndex, setFocusedIndex] = useState<number>(0);\n  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);\n\n  // This function handles changes in the input fields.\n  const handleChange = (element: HTMLInputElement, index: number) => {\n    // Only allow numbers to be entered\n    if (isNaN(Number(element.value)) || element.value === ' ') {\n        element.value = '';\n        return;\n    };\n\n    // Update the code array with the new value\n    const newCode = [...code];\n    newCode[index] = element.value;\n    setCode(newCode);\n\n    // If there's a value and it's not the last input, focus the next one\n    if (element.value && index < 4) {\n      const nextInput = inputRefs.current[index + 1];\n      if (nextInput) {\n        nextInput.focus();\n      }\n    }\n  };\n\n  // This function handles key presses, specifically for Backspace.\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {\n    // If backspace is pressed and the input is empty, focus the previous input\n    if (e.key === 'Backspace' && !code[index] && index > 0) {\n      const prevInput = inputRefs.current[index - 1];\n      if (prevInput) {\n        prevInput.focus();\n      }\n    }\n  };\n\n  // This function handles pasting content into the input fields.\n  const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    const pasteData = e.clipboardData.getData('text').slice(0, 5);\n    if (!/^\\d+$/.test(pasteData)) return; // Only paste if it's all digits\n\n    const newCode = new Array(5).fill('');\n    for (let i = 0; i < pasteData.length; i++) {\n        newCode[i] = pasteData[i];\n    }\n    setCode(newCode);\n    \n    // Focus on the last input that was filled by the paste\n    const lastFullInput = Math.min(pasteData.length - 1, 4);\n    if(lastFullInput >= 0) {\n        const targetInput = inputRefs.current[lastFullInput];\n        if (targetInput) {\n            targetInput.focus();\n        }\n    }\n  }\n\n  // Set focus to the first input on initial render\n  useEffect(() => {\n    const firstInput = inputRefs.current[0];\n    if(firstInput) {\n      firstInput.focus();\n    }\n  }, []);\n\n  return (\n    <div className=\"flex items-center justify-center font-sans p-4\">\n        {/* Main Card */}\n      <div className=\"bg-white dark:bg-[#161B22] border border-gray-200 dark:border-gray-800 p-6 sm:p-8 rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/20 max-w-sm w-full text-center text-gray-900 dark:text-white relative overflow-hidden\">\n        \n        {/* Mac-style dots */}\n        <div className=\"absolute top-4 left-4 flex space-x-2\">\n            <div className=\"w-3 h-3 bg-[#FF5F56] rounded-full\"></div>\n            <div className=\"w-3 h-3 bg-[#FFBD2E] rounded-full\"></div>\n            <div className=\"w-3 h-3 bg-[#27C93F] rounded-full\"></div>\n        </div>\n\n        <div className=\"relative z-10\">\n          <div className=\"flex justify-center mb-4 mt-8 sm:mt-4\">\n            <div className=\"w-40 h-40 flex items-center justify-center\">\n                 {/* New Logo Image with Glow */}\n                 <img \n                    src=\"https://i.postimg.cc/SKSNJ5SQ/White-Letter-S-Logo-Instagram-Post.png\" \n                    alt=\"Logo\" \n                    className=\"w-32 h-32 object-cover rounded-lg drop-shadow-[0_0_15px_rgba(59,130,246,0.5)]\"\n                    onError={(e) => { \n                      const target = e.target as HTMLImageElement;\n                      target.onerror = null; \n                      target.src='https://placehold.co/128x128/161B22/FFFFFF?text=S'; \n                    }}\n                 />\n            </div>\n          </div>\n\n          <h1 className=\"text-2xl font-bold mb-2 text-gray-900 dark:text-gray-200\">Sign In With Two-Step Verification</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-6 text-sm\">\n            We&apos;ve sent a 5 digit code to **********060\n          </p>\n\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4 text-sm text-left font-medium\">Enter the code you received</p>\n\n          <div className=\"flex justify-center gap-2 sm:gap-3 mb-8\" onPaste={handlePaste}>\n            {code.map((data, index) => (\n              <input\n                key={index}\n                ref={(el) => {\n                  inputRefs.current[index] = el;\n                }}\n                type=\"tel\"\n                maxLength={1}\n                value={data}\n                placeholder=\"•\"\n                onChange={(e) => handleChange(e.target, index)}\n                onKeyDown={(e) => handleKeyDown(e, index)}\n                onFocus={(e) => {\n                    e.target.select()\n                    setFocusedIndex(index)\n                }}\n                onBlur={() => setFocusedIndex(-1)}\n                className={`w-12 h-14 sm:w-14 sm:h-16 text-center text-2xl font-semibold bg-gray-50 dark:bg-[#0D1117] text-gray-900 dark:text-white rounded-lg outline-none transition-all placeholder-gray-400 dark:placeholder-gray-600\n                  ${focusedIndex === index \n                    ? 'border-2 border-blue-500' \n                    : 'border border-dashed border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600'\n                  }`}\n              />\n            ))}\n          </div>\n\n          <p className=\"text-gray-500 dark:text-gray-500 text-sm\">\n            Didn&apos;t receive a code?{' '}\n            <button className=\"text-blue-600 dark:text-blue-500 hover:text-blue-500 dark:hover:text-blue-400 font-semibold focus:outline-none focus:underline\">\n              Resent code\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "type": "registry:ui"}]}