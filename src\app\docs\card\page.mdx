import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";

import CardView from "./card-view";


<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<CardView />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/card/card-view.tsx" />
  </TabsContent>
</Tabs>

# 🚀 Installation
You can add this component to your project using the CLI or by manually copying the code.

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add card.json`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/app/docs/card/card.tsx" />
  </TabsContent>
</Tabs>
