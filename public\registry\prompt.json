{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "prompt", "title": "prompt", "description": "A prompt component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "prompt.tsx", "content": "\"use client\"\nimport React, { useState, useEffect, useRef } from 'react';\n\n// Icons for the component\nimport { Plus, ChevronDown, Mic, ArrowUp, Image as ImageIcon, Box, FileText, Brain } from 'lucide-react';\n\nconst PromptComponent = () => {\n  const [prompt, setPrompt] = useState('');\n\n  // State to manage the visibility of popups and dropdowns\n  const [isAddPopupOpen, setAddPopupOpen] = useState(false);\n  const [isModelOpen, setModelOpen] = useState(false);\n\n  // State for the selected model\n  const [selectedModel, setSelectedModel] = useState('Brainwave 2.5');\n  const models = ['Brainwave 2.5', 'Creative Fusion', 'Visionary AI 3.0'];\n\n  // Refs for the popups to detect outside clicks\n  const addPopupRef = useRef<HTMLDivElement>(null);\n  const modelRef = useRef<HTMLDivElement>(null);\n  \n  // Effect to handle clicks outside of the popups/dropdowns\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (addPopupRef.current && !addPopupRef.current.contains(event.target as Node)) {\n        setAddPopupOpen(false);\n      }\n      if (modelRef.current && !modelRef.current.contains(event.target as Node)) {\n        setModelOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleModelSelect = (model: string) => {\n    setSelectedModel(model);\n    setModelOpen(false);\n  };\n  \n  const handleUpload = () => {\n    if(!prompt.trim()) {\n        // Using console.error instead of alert\n        console.error(\"Please describe your 3D object or scene first!\");\n        return;\n    }\n    console.log(`Uploading prompt: \"${prompt}\" with model: ${selectedModel}`);\n    // Add your upload logic here\n  }\n\n  const addMenuItems = [\n    { icon: <ImageIcon size={20} className=\"text-gray-500 dark:text-gray-400\" />, text: \"Add photos or videos\" },\n    { icon: <Box size={20} className=\"text-gray-500 dark:text-gray-400\" />, text: \"Add 3D objects\" },\n    { icon: <FileText size={20} className=\"text-gray-500 dark:text-gray-400\" />, text: \"Add files (docs, txt...)\" },\n  ];\n\n  return (\n    <div className=\"w-full max-w-2xl p-4\">\n      <div className=\"bg-white/80 backdrop-blur-xl dark:bg-black/90 rounded-3xl shadow-2xl border border-gray-200/50 dark:border-gray-800/50 p-6 transition-all duration-300 hover:shadow-3xl\">\n        <textarea\n          className=\"w-full p-3 bg-transparent text-gray-800 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none resize-none text-base font-medium leading-relaxed\"\n          rows={2}\n          placeholder=\"Describe your 3D object or scene...\"\n          value={prompt}\n          onChange={(e) => setPrompt(e.target.value)}\n        />\n        {/* Responsive container for controls */}\n        <div className=\"flex flex-col md:flex-row items-center justify-between mt-4 gap-4 md:gap-0\">\n          {/* Left side controls */}\n          <div className=\"flex flex-wrap items-center gap-3\">\n            {/* Add Button and Popup */}\n            <div className=\"relative\" ref={addPopupRef}>\n              <button \n                onClick={() => setAddPopupOpen(!isAddPopupOpen)}\n                className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-800 dark:hover:to-gray-700 text-gray-600 dark:text-gray-300 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl border border-gray-200/50 dark:border-gray-700/50\"\n              >\n                <Plus size={22} />\n              </button>\n              {isAddPopupOpen && (\n                <div className=\"absolute bottom-full left-0 mb-3 w-72 bg-white/95 backdrop-blur-xl dark:bg-gray-900/95 rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 z-10\">\n                  <ul>\n                    {addMenuItems.map((item, index) => (\n                       <li key={index} className=\"flex items-center gap-4 p-4 hover:bg-gray-50/80 dark:hover:bg-gray-800/80 cursor-pointer rounded-xl transition-colors duration-200 first:rounded-t-2xl last:rounded-b-2xl\">\n                         {item.icon}\n                         <span className=\"font-medium text-gray-700 dark:text-gray-200\">{item.text}</span>\n                       </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n            \n            {/* Model Selection Button and Dropdown */}\n            <div className=\"relative\" ref={modelRef}>\n              <button onClick={() => setModelOpen(!isModelOpen)} className=\"flex items-center justify-center h-12 px-4 lg:px-5 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-800/30 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-blue-800/40 dark:hover:to-indigo-700/40 text-gray-800 dark:text-gray-200 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl border border-blue-200/50 dark:border-blue-700/30\">\n                <Brain size={18} className=\"text-blue-600 dark:text-blue-400\" />\n                <span className=\"font-semibold ml-2 hidden lg:block\">{selectedModel}</span>\n                <ChevronDown size={16} className=\"ml-2 hidden lg:block\" />\n              </button>\n               {isModelOpen && (\n                 <div className=\"absolute bottom-full left-0 mb-3 w-64 bg-white/95 backdrop-blur-xl dark:bg-gray-900/95 rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 z-10\">\n                  <ul>\n                    {models.map((model) => (\n                       <li key={model} onClick={() => handleModelSelect(model)} className=\"p-4 hover:bg-gray-50/80 dark:hover:bg-gray-800/80 cursor-pointer font-medium text-gray-700 dark:text-gray-200 rounded-xl transition-colors duration-200 first:rounded-t-2xl last:rounded-b-2xl\">\n                         {model}\n                       </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          </div>\n          {/* Right side controls */}\n          <div className=\"flex items-center gap-3\">\n            <button onClick={() => console.log(\"Mic clicked\")} className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-800 dark:hover:to-gray-700 text-gray-600 dark:text-gray-300 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl border border-gray-200/50 dark:border-gray-700/50\">\n                <Mic size={22} />\n            </button>\n            <button onClick={handleUpload} className={`flex items-center justify-center w-12 h-12 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl ${\n              prompt.trim() \n                ? 'bg-gradient-to-br from-gray-800 to-gray-900 hover:from-gray-900 hover:to-black dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-500 dark:hover:to-blue-600 text-white' \n                : 'bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'\n            }`}>\n              <ArrowUp size={22} />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PromptComponent;", "type": "registry:ui"}]}