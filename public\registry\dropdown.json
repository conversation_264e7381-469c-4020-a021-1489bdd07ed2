{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "dropdown", "title": "dropdown", "description": "A dropdown component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "dropdown.tsx", "content": "\"use client\"\nimport React, { useState, useEffect, useRef, ReactNode } from 'react';\n\n// --- UPDATED SVG ICONS TO MATCH THE NEW IMAGE ---\nconst User = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><path d=\"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/></svg>\n);\n\nconst Community = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"/></svg>\n);\n\nconst Subscription = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\"/><line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\"/></svg>\n);\n\nconst Settings = (props: React.SVGProps<SVGSVGElement>) => (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><path d=\"M14 6m-2 0a2 2 0 1 0 4 0a2 2 0 1 0-4 0\"/><path d=\"M4 6h8\"/><path d=\"M16 6h4\"/><path d=\"M10 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0-4 0\"/><path d=\"M4 18h4\"/><path d=\"M12 18h8\"/><path d=\"M10 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0-4 0\"/><path d=\"M4 12h4\"/><path d=\"M12 12h8\"/></svg>\n);\n\nconst HelpCenter = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"/><line x1=\"12\" x2=\"12.01\" y1=\"17\" y2=\"17\"/></svg>\n);\n\nconst SignOut = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}><path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"/><polyline points=\"16 17 21 12 16 7\"/><line x1=\"21\" x2=\"9\" y1=\"12\" y2=\"12\"/></svg>\n);\n\n// --- Dropdown Menu Components ---\n\ninterface DropdownMenuProps {\n    children: ReactNode;\n    trigger: ReactNode;\n}\n\nconst DropdownMenu = ({ children, trigger }: DropdownMenuProps) => {\n    const [isOpen, setIsOpen] = useState(false);\n    const dropdownRef = useRef<HTMLDivElement>(null);\n\n    // Close dropdown when clicking outside\n    useEffect(() => {\n        const handleClickOutside = (event: MouseEvent) => {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n                setIsOpen(false);\n            }\n        };\n        document.addEventListener('mousedown', handleClickOutside);\n        return () => {\n            document.removeEventListener('mousedown', handleClickOutside);\n        };\n    }, []);\n\n    const handleTriggerClick = (e: React.MouseEvent) => {\n        e.stopPropagation();\n        setIsOpen(!isOpen);\n    }\n\n    return (\n        <div className=\"relative inline-block text-left\" ref={dropdownRef}>\n            <div onClick={handleTriggerClick} className=\"cursor-pointer\">\n                {trigger}\n            </div>\n            {isOpen && (\n                <div \n                    className=\"origin-top-right absolute right-0 mt-2 w-64 rounded-2xl shadow-xl bg-white dark:bg-zinc-900 ring-1 ring-black ring-opacity-5 focus:outline-none z-50 animate-in fade-in-0 zoom-in-95 p-2\"\n                    role=\"menu\" \n                    aria-orientation=\"vertical\"\n                >\n                    {children}\n                </div>\n            )}\n        </div>\n    );\n};\n\n// Added `active` prop for styling the highlighted item\ninterface DropdownMenuItemProps {\n    children: ReactNode;\n    onClick?: () => void;\n    active?: boolean;\n}\n\nconst DropdownMenuItem = ({ children, onClick, active = false }: DropdownMenuItemProps) => (\n    <a\n        href=\"#\"\n        onClick={(e: React.MouseEvent) => {\n            e.preventDefault();\n            if(onClick) onClick();\n        }}\n        className={`\n            text-zinc-800 dark:text-zinc-200 font-medium group flex items-center \n            px-3 py-2.5 text-sm rounded-lg transition-colors duration-150\n            ${active \n                ? 'bg-zinc-100 dark:bg-zinc-800' \n                : 'hover:bg-zinc-100 dark:hover:bg-zinc-800'\n            }\n        `}\n        role=\"menuitem\"\n    >\n        {children}\n    </a>\n);\n\nconst DropdownMenuSeparator = () => (\n    <div className=\"my-2 h-px bg-zinc-200 dark:bg-zinc-700\" />\n);\n\n// --- Main App Component ---\n\nexport default function Dropdown() {\n    return (\n        <div className=\" flex items-center justify-center font-sans p-8\">\n            <DropdownMenu \n                trigger={\n                    <button className=\"px-5 py-2 text-sm font-semibold text-zinc-800 dark:text-zinc-100 bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-sm hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-colors\">\n                        Open Menu\n                    </button>\n                }\n            >\n                <div className=\"flex flex-col space-y-1\">\n                    <DropdownMenuItem onClick={() => console.log('Profile clicked')} active={true}>\n                        <User className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Profile</span>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem onClick={() => console.log('Community clicked')}>\n                        <Community className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Community</span>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem onClick={() => console.log('Subscription clicked')}>\n                        <Subscription className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Subscription</span>\n                        <span className=\"ml-auto text-xs font-bold text-white bg-gradient-to-r from-pink-500 to-purple-500 rounded-full px-2 py-0.5\">PRO</span>\n                    </DropdownMenuItem>\n                     <DropdownMenuItem onClick={() => console.log('Settings clicked')}>\n                        <Settings className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Settings</span>\n                    </DropdownMenuItem>\n                </div>\n                <DropdownMenuSeparator />\n                 <div className=\"flex flex-col space-y-1\">\n                    <DropdownMenuItem onClick={() => console.log('Help Center clicked')}>\n                        <HelpCenter className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Help center</span>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem onClick={() => console.log('Sign Out clicked')}>\n                        <SignOut className=\"mr-3 h-5 w-5 text-zinc-500\" />\n                        <span>Sign out</span>\n                    </DropdownMenuItem>\n                </div>\n            </DropdownMenu>\n        </div>\n    );\n}\n", "type": "registry:ui"}]}