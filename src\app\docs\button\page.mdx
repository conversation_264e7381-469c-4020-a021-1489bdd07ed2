import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  <PERSON><PERSON>List,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";

import ButtonView from "./button-view"
import SocialButton from "./social-button"
import ActionButton from "./action"
import RightIconButton from "./righticon"
import OnlyIconButton from "./only"
import CommerceButton from "./commerce"
import OthersButton from "./others"

# Buttons
A customizable and interactive button component with a built-in ripple effect.

## Basic Variants
<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<ButtonView />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/button-view.tsx" />
  </TabsContent>
</Tabs>

## Others


<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<OthersButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/others.tsx" />
  </TabsContent>
</Tabs>



## Social Buttons

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<SocialButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/social-button.tsx" />
  </TabsContent>
</Tabs>

## Action Buttons

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<ActionButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/action.tsx" />
  </TabsContent>
</Tabs>

## Right Icon Buttons

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<RightIconButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/righticon.tsx" />
  </TabsContent>
</Tabs>

## Icon Only Buttons

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<OnlyIconButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/only.tsx" />
  </TabsContent>
</Tabs>

## E-commerce Buttons

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<CommerceButton />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/button/commerce.tsx" />
  </TabsContent>
</Tabs>


# Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add button.json`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/app/docs/button/button.tsx" />
  </TabsContent>
</Tabs>




# Button Component

A customizable and interactive button component with a built-in ripple effect.

## Installation & Import

First, make sure the `Button` component file is included in your project. Then, you can import it into your MDX or React files like this:

```tsx
import { Button } from './path/to/your/Button';
```

## Basic Usage

Here's the most basic way to use the component:

```tsx live
<Button variant="default">Click Me</Button>
```

## Variants

The button comes in several variants to suit different UI contexts.

## Default

The standard, primary button for your application.

```tsx live
<Button variant="default">Default Button</Button>
```

## Destructive

Use this for actions that could have destructive consequences, like deleting data.

```tsx live
<Button variant="destructive">Delete Action</Button>
```

## Outline

A lower-emphasis button with a transparent background and a border.

```tsx live
<Button variant="outline">Outline Button</Button>
```

## Secondary

For actions that are secondary to the main call-to-action on a page.

```tsx live
<Button variant="secondary">Secondary Button</Button>
```

## Ghost

The lowest-emphasis button, used for supplemental actions that need to be available but not prominent.

```tsx live
<Button variant="ghost">Ghost Button</Button>
```

## Link

A button that looks and behaves like a hyperlink.

```tsx live
<Button variant="link">Link Button</Button>
```

## Sizes

The button is available in three sizes.

```tsx live
<div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
  <Button variant="default" size="sm">Small</Button>
  <Button variant="default" size="default">Default</Button>
  <Button variant="default" size="lg">Large</Button>
</div>
```

## Loading State

You can put the button into a loading state, which disables it and shows a spinner. This is useful for indicating that an action (like a form submission) is in progress.

```tsx live
<div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
    <Button variant="default" loading>Loading...</Button>
    <Button variant="outline" loading>Loading...</Button>
</div>
```

# Props

<table border="1" cellspacing="0" cellpadding="8">
  <thead>
    <tr>
      <th>Prop</th>
      <th>Type</th>
      <th>Default</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>variant</code></td>
      <td><code>'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'</code></td>
      <td><code>'default'</code></td>
      <td>The visual style of the button.</td>
    </tr>
    <tr>
      <td><code>size</code></td>
      <td><code>'default' | 'sm' | 'lg'</code></td>
      <td><code>'default'</code></td>
      <td>The size of the button.</td>
    </tr>
    <tr>
      <td><code>loading</code></td>
      <td><code>boolean</code></td>
      <td><code>false</code></td>
      <td>If <code>true</code>, the button will be disabled and show a loading spinner.</td>
    </tr>
    <tr>
      <td><code>...props</code></td>
      <td><code>React.ButtonHTMLAttributes&lt;HTMLButtonElement&gt;</code></td>
      <td></td>
      <td>Any other standard <code>button</code> attributes (e.g., <code>onClick</code>, <code>disabled</code>, <code>className</code>) will be passed through.</td>
    </tr>
  </tbody>
</table>
