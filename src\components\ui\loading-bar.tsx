"use client";

import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "motion/react";

interface LoadingBarProps {
  isLoading: boolean;
  color?: string;
  height?: number;
  duration?: number;
}

export const LoadingBar: React.FC<LoadingBarProps> = ({
  isLoading,
  height = 3,
  duration = 0.6,
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setProgress(0);
      setIsVisible(true);

      // More realistic YouTube-style progress
      const intervals: NodeJS.Timeout[] = [];

      // Fast initial progress
      intervals.push(setTimeout(() => setProgress(30), 100));
      intervals.push(setTimeout(() => setProgress(50), 300));
      intervals.push(setTimeout(() => setProgress(70), 600));

      // Slower progress towards the end
      const slowInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 5;
        });
      }, 200);
      intervals.push(slowInterval);

      return () => {
        intervals.forEach(clearTimeout);
        clearInterval(slowInterval);
      };
    } else {
      // Complete the loading
      setProgress(100);
      const timeout = setTimeout(() => {
        setProgress(0);
        setIsVisible(false);
      }, 400);
      return () => clearTimeout(timeout);
    }
  }, [isLoading]);

  return (
    <div className="loading-bar-container">
      <AnimatePresence>
        {(isLoading || isVisible) && (
          <motion.div
            className="w-full h-full"
            initial={{ opacity: 0, scaleX: 0 }}
            animate={{ opacity: 1, scaleX: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{ transformOrigin: "left" }}
          >
            <motion.div
              className="loading-bar"
              initial={{ width: "0%" }}
              animate={{ width: `${progress}%` }}
              transition={{
                duration: isLoading ? duration : 0.3,
                ease: isLoading ? "easeOut" : "easeInOut",
              }}
            >
              {/* Enhanced glass reflection effect */}
              {isLoading && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                  animate={{
                    x: ["-100%", "200%"],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  style={{ width: "40%" }}
                />
              )}

              {/* Top highlight for enhanced glass effect */}
              <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-white/60 to-transparent" />

              {/* Bottom subtle shadow */}
              <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-black/10 to-transparent" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
