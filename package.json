{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build:registry": "tsx ./src/scripts/build-registry.ts"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.2", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-tabs": "^1.1.11", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "commander": "^14.0.0", "framer-motion": "^12.19.1", "lucide-react": "^0.510.0", "motion": "^12.11.3", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^4.12.0", "react-responsive": "^10.0.1", "remark-gfm": "^4.0.1", "simplex-noise": "^4.0.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "shiki": "^3.4.0", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}